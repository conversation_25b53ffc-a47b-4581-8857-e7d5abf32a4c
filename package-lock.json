{"name": "@ghq-abi/northstar-domain", "version": "1.0.3", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@ghq-abi/northstar-domain", "version": "1.0.3", "license": "ISC", "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/typeorm": "^11.0.0", "dotenv": "^17.2.1", "mssql": "^11.0.1", "typeorm": "^0.3.25"}, "devDependencies": {"@types/jest": "^29.5.2", "@types/mssql": "^9.1.7", "@types/node": "^20.12.5", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.6.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^4.9.3"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@azure-rest/core-client": {"version": "2.5.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure-rest/core-client/-/core-client-2.5.0.tgz", "integrity": "sha1-GxmwW9Ej/PTG6oLjlYU1MSCuIxA=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.9.0", "@azure/core-rest-pipeline": "^1.5.0", "@azure/core-tracing": "^1.0.1", "@typespec/ts-http-runtime": "^0.3.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha1-Qv4MyrI4QdmQWBLFjxCC0neEVm0=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-auth": {"version": "1.10.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-auth/-/core-auth-1.10.0.tgz", "integrity": "sha1-aNunA2CA4dnVaZxOSCFKt5b6c60=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.11.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/core-client": {"version": "1.10.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-client/-/core-client-1.10.0.tgz", "integrity": "sha1-n07JyJpjUWknhArmIMYOgRoLVKM=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-rest-pipeline": "^1.20.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.6.1", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/core-http-compat": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-http-compat/-/core-http-compat-2.3.0.tgz", "integrity": "sha1-6dOWKZIR50IwiCdnQILBO9Y4xr8=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-client": "^1.3.0", "@azure/core-rest-pipeline": "^1.20.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-lro": {"version": "2.7.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-lro/-/core-lro-2.7.2.tgz", "integrity": "sha1-eHEFAnog5Fx3ZRqYsBpNOwG3Wgg=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.2.0", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-paging": {"version": "1.6.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-paging/-/core-paging-1.6.2.tgz", "integrity": "sha1-QNOGDcLffykdZjULLP2RcVJkM+c=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-rest-pipeline": {"version": "1.22.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-rest-pipeline/-/core-rest-pipeline-1.22.0.tgz", "integrity": "sha1-duRKdQk6L0d/xUuE9GBJ3CzmWAA=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.8.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@typespec/ts-http-runtime": "^0.3.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/core-tracing": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-tracing/-/core-tracing-1.3.0.tgz", "integrity": "sha1-NBFT9bKSdTnriYV3ZR7kjOmN2iU=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/core-util": {"version": "1.13.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/core-util/-/core-util-1.13.0.tgz", "integrity": "sha1-/Cg0/FHh4rt0twwoS0D4JNhnQio=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@typespec/ts-http-runtime": "^0.3.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/identity": {"version": "4.10.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/identity/-/identity-4.10.2.tgz", "integrity": "sha1-ZgnOOYgk/wu1PxrRBDqfHMk+Vrg=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.9.0", "@azure/core-client": "^1.9.2", "@azure/core-rest-pipeline": "^1.17.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@azure/msal-browser": "^4.2.0", "@azure/msal-node": "^3.5.0", "open": "^10.1.0", "tslib": "^2.2.0"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/keyvault-common": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/keyvault-common/-/keyvault-common-2.0.0.tgz", "integrity": "sha1-keUN8B2b+o9V8Qe7nNvFdYaysqQ=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.3.0", "@azure/core-client": "^1.5.0", "@azure/core-rest-pipeline": "^1.8.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.10.0", "@azure/logger": "^1.1.4", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/keyvault-keys": {"version": "4.10.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/keyvault-keys/-/keyvault-keys-4.10.0.tgz", "integrity": "sha1-dUdujyhYDcI7vJ2sbRZU4THW79U=", "license": "MIT", "dependencies": {"@azure-rest/core-client": "^2.3.3", "@azure/abort-controller": "^2.1.2", "@azure/core-auth": "^1.9.0", "@azure/core-http-compat": "^2.2.0", "@azure/core-lro": "^2.7.2", "@azure/core-paging": "^1.6.2", "@azure/core-rest-pipeline": "^1.19.0", "@azure/core-tracing": "^1.2.0", "@azure/core-util": "^1.11.0", "@azure/keyvault-common": "^2.0.0", "@azure/logger": "^1.1.4", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/logger": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/logger/-/logger-1.3.0.tgz", "integrity": "sha1-VQHPhdT1JjBgKozHXfdlaMlpqCc=", "license": "MIT", "dependencies": {"@typespec/ts-http-runtime": "^0.3.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@azure/msal-browser": {"version": "4.16.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-browser/-/msal-browser-4.16.0.tgz", "integrity": "sha1-FbFWf2hz9ksNQ2ti8QaM4B/H8JA=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.9.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "15.9.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-common/-/msal-common-15.9.0.tgz", "integrity": "sha1-SbYqeY3RtHtBDm5UD9NgCfHU0Y4=", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-node": {"version": "3.6.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@azure/msal-node/-/msal-node-3.6.4.tgz", "integrity": "sha1-k38ON+c9SN+2irjzpQOgzyGmUoU=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.9.0", "jsonwebtoken": "^9.0.0", "uuid": "^8.3.0"}, "engines": {"node": ">=16"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz", "integrity": "sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/core/-/core-7.28.0.tgz", "integrity": "sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/generator/-/generator-7.28.0.tgz", "integrity": "sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha1-2wu8+6WAL573hwcFp++HiFCO3gI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.28.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/helpers/-/helpers-7.28.2.tgz", "integrity": "sha1-gPCRj+y/6+qa+FbEGXYyMAQO6FA=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "integrity": "sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/template/-/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/traverse/-/traverse-7.28.0.tgz", "integrity": "sha1-UYqhEzWbBiBCN54zPbGDgLU340s=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@babel/types/-/types-7.28.2.tgz", "integrity": "sha1-2p2whWqaiOChOwGYgddRNYjPcSs=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "integrity": "sha1-daLotRy3WKdVPWgEpZMteqznXDk=", "dev": true, "license": "MIT"}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "integrity": "sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=", "devOptional": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=", "devOptional": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "integrity": "sha1-OIomnw8lwbatwxe1osVXFIlMcK0=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "8.57.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@eslint/js/-/js-8.57.1.tgz", "integrity": "sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "integrity": "sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "integrity": "sha1-Siho111taWPkI7z5C3/RvjQ0CdM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz", "integrity": "sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "integrity": "sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "integrity": "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/argparse": {"version": "1.0.10", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/argparse/-/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml": {"version": "3.14.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/load-nyc-config/node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/console/-/console-29.7.0.tgz", "integrity": "sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/core": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/core/-/core-29.7.0.tgz", "integrity": "sha1-tszMI58w/zZglljFpeIpF1fORI8=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/environment": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/environment/-/environment-29.7.0.tgz", "integrity": "sha1-JNYfVP8feG881Ac7S5RBY4O68qc=", "dev": true, "license": "MIT", "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/expect/-/expect-29.7.0.tgz", "integrity": "sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "integrity": "sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "integrity": "sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/globals": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/globals/-/globals-29.7.0.tgz", "integrity": "sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/reporters": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/reporters/-/reporters-29.7.0.tgz", "integrity": "sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=", "dev": true, "license": "MIT", "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/schemas": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/schemas/-/schemas-29.6.3.tgz", "integrity": "sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/source-map/-/source-map-29.6.3.tgz", "integrity": "sha1-2Quncglc83o0peuUE/G1YqCFVMQ=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-result": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/test-result/-/test-result-29.7.0.tgz", "integrity": "sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "integrity": "sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/transform/-/transform-29.7.0.tgz", "integrity": "sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jest/types/-/types-29.6.3.tgz", "integrity": "sha1-ETH4z2NOfoTF53urEvBSr1hfulk=", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "integrity": "sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "devOptional": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=", "devOptional": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "integrity": "sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@js-joda/core": {"version": "5.6.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@js-joda/core/-/core-5.6.5.tgz", "integrity": "sha1-x2aJS0nrgERIC5FiX7fcNw6Bgu8=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@lukeed/csprng": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@lukeed/csprng/-/csprng-1.1.0.tgz", "integrity": "sha1-Hj5L0Fwcx6Cy3b2KA/OfbktebP4=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@nestjs/common": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/common/-/common-11.1.5.tgz", "integrity": "sha1-vEkdBJ4h+RIWGJsG5W+AFzCoo9E=", "license": "MIT", "dependencies": {"file-type": "21.0.0", "iterare": "1.2.1", "load-esm": "1.0.2", "tslib": "2.8.1", "uid": "2.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"class-transformer": ">=0.4.1", "class-validator": ">=0.13.2", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"class-transformer": {"optional": true}, "class-validator": {"optional": true}}}, "node_modules/@nestjs/config": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/config/-/config-4.0.2.tgz", "integrity": "sha1-ond6H9LQ1ZS6s5U/UPvKlcFMzlI=", "license": "MIT", "dependencies": {"dotenv": "16.4.7", "dotenv-expand": "12.0.1", "lodash": "4.17.21"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "rxjs": "^7.1.0"}}, "node_modules/@nestjs/config/node_modules/dotenv": {"version": "16.4.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.4.7.tgz", "integrity": "sha1-DiDFuClQFAqpm+NgqKX1IzX1PCY=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/@nestjs/core": {"version": "11.1.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/core/-/core-11.1.6.tgz", "integrity": "sha1-nVSILxIRaLL6Kwf6HbCFgWGoBiY=", "hasInstallScript": true, "license": "MIT", "dependencies": {"@nuxt/opencollective": "0.4.1", "fast-safe-stringify": "2.1.1", "iterare": "1.2.1", "path-to-regexp": "8.2.0", "tslib": "2.8.1", "uid": "2.0.2"}, "engines": {"node": ">= 20"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nest"}, "peerDependencies": {"@nestjs/common": "^11.0.0", "@nestjs/microservices": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/websockets": "^11.0.0", "reflect-metadata": "^0.1.12 || ^0.2.0", "rxjs": "^7.1.0"}, "peerDependenciesMeta": {"@nestjs/microservices": {"optional": true}, "@nestjs/platform-express": {"optional": true}, "@nestjs/websockets": {"optional": true}}}, "node_modules/@nestjs/typeorm": {"version": "11.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nestjs/typeorm/-/typeorm-11.0.0.tgz", "integrity": "sha1-sPRdaQI5bbieCsH05zjC/zQHt5Q=", "license": "MIT", "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "@nestjs/core": "^10.0.0 || ^11.0.0", "reflect-metadata": "^0.1.13 || ^0.2.0", "rxjs": "^7.2.0", "typeorm": "^0.3.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@nuxt/opencollective": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@nuxt/opencollective/-/opencollective-0.4.1.tgz", "integrity": "sha1-V7xB0rA7L7oguTXBWVCsD0vSzqI=", "license": "MIT", "dependencies": {"consola": "^3.2.3"}, "bin": {"opencollective": "bin/opencollective.js"}, "engines": {"node": "^14.18.0 || >=16.10.0", "npm": ">=5.10.0"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "integrity": "sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@pkgr/core": {"version": "0.2.9", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@pkgr/core/-/core-0.2.9.tgz", "integrity": "sha1-0imnt/nawWehVpku8jx/AjZT9Ts=", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=", "dev": true, "license": "MIT"}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinonjs/commons/-/commons-3.0.1.tgz", "integrity": "sha1-ECk1fkTKkBphVYX20nc428iQhM0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@sqltools/formatter": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@sqltools/formatter/-/formatter-1.2.5.tgz", "integrity": "sha1-OrwgPHm4w+kP1sFWoMYtVANSDhI=", "license": "MIT"}, "node_modules/@tediousjs/connection-string": {"version": "0.5.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tediousjs/connection-string/-/connection-string-0.5.0.tgz", "integrity": "sha1-mz2FjAQKrGvfVYS/RTcM71tlIrQ=", "license": "MIT"}, "node_modules/@tokenizer/inflate": {"version": "0.2.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tokenizer/inflate/-/inflate-0.2.7.tgz", "integrity": "sha1-Mt2d/Jq+RXyJs9m3YPwGkMhaEDs=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "fflate": "^0.8.2", "token-types": "^6.0.0"}, "engines": {"node": ">=18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/@tokenizer/token": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tokenizer/token/-/token-0.3.0.tgz", "integrity": "sha1-/pipP+eJJH6ZjHXnTpx8YyF6onY=", "license": "MIT"}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha1-5DhjFihPALmENb9A9y91oJ2r9sE=", "devOptional": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=", "devOptional": true, "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "integrity": "sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "integrity": "sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "integrity": "sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.14", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/jest/-/jest-29.5.14.tgz", "integrity": "sha1-K5EJEvodaFbK3NDB+Vr33x1gSeU=", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/mssql": {"version": "9.1.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/mssql/-/mssql-9.1.7.tgz", "integrity": "sha1-QBdjGSq87qi2L6uIa6WeCLVb5ic=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "tarn": "^3.0.1", "tedious": "*"}}, "node_modules/@types/node": {"version": "20.19.9", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/node/-/node-20.19.9.tgz", "integrity": "sha1-yppYGT/sNhzG6FnYi1ImGFPx8NM=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/readable-stream": {"version": "4.0.21", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/readable-stream/-/readable-stream-4.0.21.tgz", "integrity": "sha1-cWVYRUpeDDwGUVIPgVTvwyiPWcs=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/stack-utils/-/stack-utils-2.0.3.tgz", "integrity": "sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=", "dev": true, "license": "MIT"}, "node_modules/@types/yargs": {"version": "17.0.33", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/yargs/-/yargs-17.0.33.tgz", "integrity": "sha1-jDIwPag+7AUKhLPHrnufki0T4y0=", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "integrity": "sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-7.18.0.tgz", "integrity": "sha1-sW088+52v1cv31EeecJIvexhnqM=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/type-utils": "7.18.0", "@typescript-eslint/utils": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "graphemer": "^1.4.0", "ignore": "^5.3.1", "natural-compare": "^1.4.0", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/parser/-/parser-7.18.0.tgz", "integrity": "sha1-g5KNDxt/SvqXQJjGS1zm+QUflqA=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/types": "7.18.0", "@typescript-eslint/typescript-estree": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-7.18.0.tgz", "integrity": "sha1-ySjnqfwsCz7ZKrMRLGFNa9mVHIM=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/type-utils/-/type-utils-7.18.0.tgz", "integrity": "sha1-IWX/ruALH7vdLUCqhSMtq2mY9Ts=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "7.18.0", "@typescript-eslint/utils": "7.18.0", "debug": "^4.3.4", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/types/-/types-7.18.0.tgz", "integrity": "sha1-uQpXzN6nF5f//6AyHnRPN57IOMk=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-7.18.0.tgz", "integrity": "sha1-tYaNSGxRzo8xIwm6eb258zGzeTE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/utils/-/utils-7.18.0.tgz", "integrity": "sha1-vKAc3nf5X8ao1bDby/s9bKS+RR8=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/types": "7.18.0", "@typescript-eslint/typescript-estree": "7.18.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "7.18.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-7.18.0.tgz", "integrity": "sha1-BWRim2Ek1nYHN40PAzKgSVsl59c=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.18.0", "eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typespec/ts-http-runtime": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@typespec/ts-http-runtime/-/ts-http-runtime-0.3.0.tgz", "integrity": "sha1-9Qb/IXDllKJX+OeKoZYIjzpGoi0=", "license": "MIT", "dependencies": {"http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=20.0.0"}}, "node_modules/@ungap/structured-clone": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "integrity": "sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=", "dev": true, "license": "ISC"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn/-/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "devOptional": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=", "devOptional": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "7.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/agent-base/-/agent-base-7.1.4.tgz", "integrity": "sha1-48121MVI7oldPD/Y3B9sW5Ay56g=", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ansis": {"version": "3.17.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansis/-/ansis-3.17.0.tgz", "integrity": "sha1-+o2cKpP+fRF34MF/nutWKlioMtc=", "license": "ISC", "engines": {"node": ">=14"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/app-root-path": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/app-root-path/-/app-root-path-3.1.0.tgz", "integrity": "sha1-WXGi/BK6FwNpp6HvAYxx5uR8LoY=", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/arg/-/arg-4.1.3.tgz", "integrity": "sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=", "devOptional": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/array-union/-/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/babel-jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-jest/-/babel-jest-29.7.0.tgz", "integrity": "sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=", "dev": true, "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "integrity": "sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.1.tgz", "integrity": "sha1-IExhADD3501Msa19D/PARKv0hog=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^8.0.0-0"}}, "node_modules/babel-preset-jest": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "integrity": "sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=", "dev": true, "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bl": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bl/-/bl-6.1.0.tgz", "integrity": "sha1-zDXOei6EWMqoyPtd7u1lN7c+RQQ=", "license": "MIT", "dependencies": {"@types/readable-stream": "^4.0.0", "buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}}, "node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/browserslist/-/browserslist-4.25.1.tgz", "integrity": "sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bser": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bser/-/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer/-/buffer-6.0.3.tgz", "integrity": "sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-equal-constant-time": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "dev": true, "license": "MIT"}, "node_modules/bundle-name": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/bundle-name/-/bundle-name-4.1.0.tgz", "integrity": "sha1-87lrNBYNZDGhnXaIE1r3z7h5eIk=", "license": "MIT", "dependencies": {"run-applescript": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001731", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001731.tgz", "integrity": "sha1-J3wHQW6kYT7FZOWw/7R+e2DzLi8=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/char-regex/-/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "3.9.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ci-info/-/ci-info-3.9.0.tgz", "integrity": "sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cjs-module-lexer": {"version": "1.4.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "integrity": "sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=", "dev": true, "license": "MIT"}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cliui/-/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=", "dev": true, "license": "MIT"}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/commander": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/commander/-/commander-11.1.0.tgz", "integrity": "sha1-Yv3OdgBqaOXBqzMU3JLoAOuD2QY=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/consola": {"version": "3.4.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/consola/-/consola-3.4.2.tgz", "integrity": "sha1-WvEQFFOXu2ev2rdwE/3DTK5ZDqc=", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.10.0"}}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/create-jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/create-jest/-/create-jest-29.7.0.tgz", "integrity": "sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "bin": {"create-jest": "bin/create-jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/create-require/-/create-require-1.1.1.tgz", "integrity": "sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=", "devOptional": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw=", "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/debug/-/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/dedent": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dedent/-/dedent-1.6.0.tgz", "integrity": "sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=", "license": "MIT", "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/default-browser": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/default-browser/-/default-browser-5.2.1.tgz", "integrity": "sha1-e3umEgT/PkJbVWhprm0+nZ8XEs8=", "license": "MIT", "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/default-browser-id/-/default-browser-id-5.0.0.tgz", "integrity": "sha1-odmL+WDBUILYo/pp6DFQzMzDryY=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz", "integrity": "sha1-27Ga37dG1/xtc0oGty9KANAhJV8=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/detect-newline": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/detect-newline/-/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/diff/-/diff-4.0.2.tgz", "integrity": "sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/diff-sequences": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/diff-sequences/-/diff-sequences-29.6.3.tgz", "integrity": "sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dotenv": {"version": "17.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-17.2.1.tgz", "integrity": "sha1-bzLhD68BSINRVTjckioPuHZdmzI=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand": {"version": "12.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv-expand/-/dotenv-expand-12.0.1.tgz", "integrity": "sha1-RL36IEo2gQBonsNdc4V1X1mc7rE=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand/node_modules/dotenv": {"version": "16.6.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.6.1.tgz", "integrity": "sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "integrity": "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=", "license": "MIT"}, "node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz", "integrity": "sha1-rg8PothQRe8UqBfao86azQSJ5b8=", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "node_modules/electron-to-chromium": {"version": "1.5.192", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.192.tgz", "integrity": "sha1-bfxXpBhGpXsY+cASGCGm3x4WXME=", "dev": true, "license": "ISC"}, "node_modules/emittery": {"version": "0.13.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emittery/-/emittery-0.13.1.tgz", "integrity": "sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escalade/-/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.57.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint/-/eslint-8.57.1.tgz", "integrity": "sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "9.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-config-prettier/-/eslint-config-prettier-9.1.2.tgz", "integrity": "sha1-kN60+gJZWS33dLYA29HSJJp4zpE=", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "5.5.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.3.tgz", "integrity": "sha1-H4jpIgpyrIvhce7F+dTk1Sm19KA=", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-scope": {"version": "7.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-scope/-/eslint-scope-7.2.2.tgz", "integrity": "sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/espree": {"version": "9.6.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/espree/-/espree-9.6.1.tgz", "integrity": "sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exit": {"version": "0.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/exit/-/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expect": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/expect/-/expect-29.7.0.tgz", "integrity": "sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=", "dev": true, "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fast-safe-stringify": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz", "integrity": "sha1-xAaoO25w2eNc47MKgRQd8wrrqIQ=", "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fastq/-/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fb-watchman": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=", "dev": true, "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/fflate": {"version": "0.8.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fflate/-/fflate-0.8.2.tgz", "integrity": "sha1-/IYx9TR4Eq1gKLvkojCLJ5KqHeo=", "license": "MIT"}, "node_modules/file-entry-cache": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-type": {"version": "21.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/file-type/-/file-type-21.0.0.tgz", "integrity": "sha1-tsWZAGS8S3BPjlybYBDFkGTSaLw=", "license": "MIT", "dependencies": {"@tokenizer/inflate": "^0.2.7", "strtok3": "^10.2.2", "token-types": "^6.0.0", "uint8array-extras": "^1.4.0"}, "engines": {"node": ">=20"}, "funding": {"url": "https://github.com/sindresorhus/file-type?sponsor=1"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/flat-cache/-/flat-cache-3.2.0.tgz", "integrity": "sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/for-each/-/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz", "integrity": "sha1-Mujp7Rtoo0l777msK2rfkqY4V28=", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-package-type/-/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=", "dev": true, "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/globals": {"version": "13.24.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/globals/-/globals-13.24.0.tgz", "integrity": "sha1-hDKhnXjODB6DOUnDats0VAC7EXE=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/globby/-/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true, "license": "MIT"}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-local": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/import-local/-/import-local-3.2.0.tgz", "integrity": "sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true, "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-docker/-/is-docker-3.0.0.tgz", "integrity": "sha1-kAk6oxBid9inelkQ265xdH4VogA=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-inside-container/-/is-inside-container-1.0.0.tgz", "integrity": "sha1-6B+6aZZi6zHb2vJnZqYdSBRxfqQ=", "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-wsl": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/is-wsl/-/is-wsl-3.1.0.tgz", "integrity": "sha1-4cZX45wQCQr8vt7GFyD2uSTDy9I=", "license": "MIT", "dependencies": {"is-inside-container": "^1.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isarray": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/isarray/-/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "integrity": "sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "6.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "integrity": "sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha1-kIMFusmlvRdaxqdEier9D8JEWn0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/iterare": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/iterare/-/iterare-1.2.1.tgz", "integrity": "sha1-E5xAD/c2NpDjOr/6M8u6iSDwAEI=", "license": "ISC", "engines": {"node": ">=6"}}, "node_modules/jackspeak": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jackspeak/-/jackspeak-3.4.3.tgz", "integrity": "sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jest": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest/-/jest-29.7.0.tgz", "integrity": "sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "integrity": "sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-circus/-/jest-circus-29.7.0.tgz", "integrity": "sha1-toF6RfzINdixbVli0MAmRz7jZoo=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-cli": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-cli/-/jest-cli-29.7.0.tgz", "integrity": "sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=", "dev": true, "license": "MIT", "dependencies": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-config": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-config/-/jest-config-29.7.0.tgz", "integrity": "sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/jest-diff": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-diff/-/jest-diff-29.7.0.tgz", "integrity": "sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-docblock": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-docblock/-/jest-docblock-29.7.0.tgz", "integrity": "sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=", "dev": true, "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-each/-/jest-each-29.7.0.tgz", "integrity": "sha1-FiqbPyMovdmRvqq/+7dHReVld9E=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-environment-node": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "integrity": "sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-get-type/-/jest-get-type-29.6.3.tgz", "integrity": "sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "integrity": "sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-leak-detector": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "integrity": "sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "integrity": "sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-message-util/-/jest-message-util-29.7.0.tgz", "integrity": "sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-mock": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-mock/-/jest-mock-29.7.0.tgz", "integrity": "sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha1-kwsVRhZNStWTfVVA5xHU041MrS4=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "29.6.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "integrity": "sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-resolve/-/jest-resolve-29.7.0.tgz", "integrity": "sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "integrity": "sha1-GwTywJXzf8d2/0CAPckpIbHohCg=", "dev": true, "license": "MIT", "dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runner": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-runner/-/jest-runner-29.7.0.tgz", "integrity": "sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=", "dev": true, "license": "MIT", "dependencies": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runtime": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-runtime/-/jest-runtime-29.7.0.tgz", "integrity": "sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=", "dev": true, "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "integrity": "sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-util": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-util/-/jest-util-29.7.0.tgz", "integrity": "sha1-I8K2K/sivoK0TemAVYAv83EPwLw=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-validate/-/jest-validate-29.7.0.tgz", "integrity": "sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watcher": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-watcher/-/jest-watcher-29.7.0.tgz", "integrity": "sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=", "dev": true, "license": "MIT", "dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jest-worker/-/jest-worker-29.7.0.tgz", "integrity": "sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-md4": {"version": "0.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-md4/-/js-md4-0.3.2.tgz", "integrity": "sha1-zTs9wEWwxARVbIHdtXVsI+WdfPU=", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonwebtoken": {"version": "9.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "integrity": "sha1-Zf+R9KvvF4RpfUCVK7GZjFBMqvM=", "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^7.5.4"}, "engines": {"node": ">=12", "npm": ">=6"}}, "node_modules/jwa": {"version": "1.4.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jwa/-/jwa-1.4.2.tgz", "integrity": "sha1-FgEaxttI3nsQJ3fleJeQFSDux7k=", "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "node_modules/jws": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/jws/-/jws-3.2.2.tgz", "integrity": "sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=", "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/leven": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/leven/-/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true, "license": "MIT"}, "node_modules/load-esm": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/load-esm/-/load-esm-1.0.2.tgz", "integrity": "sha1-Ndusiho6vbgCzyNgCASPzIqSiaY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/Borewit"}, {"type": "buymeacoffee", "url": "https://buymeacoffee.com/borewit"}], "license": "MIT", "engines": {"node": ">=13.2.0"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash.includes": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.includes/-/lodash.includes-4.3.0.tgz", "integrity": "sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=", "license": "MIT"}, "node_modules/lodash.isboolean": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz", "integrity": "sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=", "license": "MIT"}, "node_modules/lodash.isinteger": {"version": "4.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=", "license": "MIT"}, "node_modules/lodash.isnumber": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz", "integrity": "sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=", "license": "MIT"}, "node_modules/lodash.isplainobject": {"version": "4.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=", "license": "MIT"}, "node_modules/lodash.isstring": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.isstring/-/lodash.isstring-4.0.1.tgz", "integrity": "sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lodash.once/-/lodash.once-4.1.1.tgz", "integrity": "sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=", "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/make-dir": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/make-dir/-/make-dir-4.0.0.tgz", "integrity": "sha1-w8IwencSd82WODBfkVwprnQbYU4=", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/make-error/-/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "devOptional": true, "license": "ISC"}, "node_modules/makeerror": {"version": "1.0.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/makeerror/-/makeerror-1.0.12.tgz", "integrity": "sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minipass/-/minipass-7.1.2.tgz", "integrity": "sha1-k6libOXl5mvU24aEnnUV6SNApwc=", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/mssql": {"version": "11.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/mssql/-/mssql-11.0.1.tgz", "integrity": "sha1-oyq3djv7s/XZcOR1Y985EfwE4h0=", "license": "MIT", "dependencies": {"@tediousjs/connection-string": "^0.5.0", "commander": "^11.0.0", "debug": "^4.3.3", "rfdc": "^1.3.0", "tarn": "^3.0.2", "tedious": "^18.2.1"}, "bin": {"mssql": "bin/mssql"}, "engines": {"node": ">=18"}}, "node_modules/native-duplexpair": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/native-duplexpair/-/native-duplexpair-1.0.0.tgz", "integrity": "sha1-eJkHjmS/PIo9cyYBs9QP8F21j6A=", "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/node-int64": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "10.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/open/-/open-10.2.0.tgz", "integrity": "sha1-udhVvgB2IOgLb7BfrJgUH+Yttzw=", "license": "MIT", "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "wsl-utils": "^0.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "integrity": "sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=", "license": "BlueOak-1.0.0"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=", "license": "ISC"}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/path-type/-/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pirates": {"version": "4.0.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pirates/-/pirates-4.0.7.tgz", "integrity": "sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/pkg-dir/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.6.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prettier/-/prettier-3.6.2.tgz", "integrity": "sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-format": {"version": "29.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pretty-format/-/pretty-format-29.7.0.tgz", "integrity": "sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "dev": true, "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pure-rand": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/pure-rand/-/pure-rand-6.1.0.tgz", "integrity": "sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "license": "MIT"}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/react-is": {"version": "18.3.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/react-is/-/react-is-18.3.1.tgz", "integrity": "sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=", "dev": true, "license": "MIT"}, "node_modules/readable-stream": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/readable-stream/-/readable-stream-4.7.0.tgz", "integrity": "sha1-ztvYoRRsE9//jasUBoAo1YwVrJE=", "license": "MIT", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/reflect-metadata": {"version": "0.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/reflect-metadata/-/reflect-metadata-0.2.2.tgz", "integrity": "sha1-QAyEW2y6h6IfLGXErrFY9PpNnFs=", "license": "Apache-2.0"}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-cwd/node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve.exports": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/resolve.exports/-/resolve.exports-2.0.3.tgz", "integrity": "sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/reusify/-/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rfdc/-/rfdc-1.4.1.tgz", "integrity": "sha1-d492xPtzHZNBTo+SX77PZMzn9so=", "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-applescript": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-applescript/-/run-applescript-7.0.0.tgz", "integrity": "sha1-5aVTwr/9Yg4WnSdsHNjxtkd4++s=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/semver/-/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/sha.js": {"version": "2.4.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sha.js/-/sha.js-2.4.12.tgz", "integrity": "sha1-64tWi/OD39GGejLD8rdOtSvb8j8=", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1", "to-buffer": "^1.2.0"}, "bin": {"sha.js": "bin.js"}, "engines": {"node": ">= 0.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "dev": true, "license": "ISC"}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true, "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.13", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/source-map-support/-/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sprintf-js": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sprintf-js/-/sprintf-js-1.1.3.tgz", "integrity": "sha1-SRS5A6L4toXRf994pw6RfocuREo=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/sql-highlight": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/sql-highlight/-/sql-highlight-6.1.0.tgz", "integrity": "sha1-40AktMbqwnRGSHce3+PB+JQVN0M=", "funding": ["https://github.com/scriptcoded/sql-highlight?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/scriptcoded"}], "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/stack-utils": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/stack-utils/-/stack-utils-2.0.6.tgz", "integrity": "sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-length": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-length/-/string-length-4.0.2.tgz", "integrity": "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=", "dev": true, "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strtok3": {"version": "10.3.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strtok3/-/strtok3-10.3.4.tgz", "integrity": "sha1-eT69DVnfJ2oIVYYTS3OkBuYL6cE=", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0"}, "engines": {"node": ">=18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/synckit": {"version": "0.11.11", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/synckit/-/synckit-0.11.11.tgz", "integrity": "sha1-wLYZzyWKl/qiCRVdnNFpm1yZjLA=", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.9"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/tarn": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tarn/-/tarn-3.0.2.tgz", "integrity": "sha1-c7YUD7uIG3FVnE+L/ePZpLPSdpM=", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/tedious": {"version": "18.6.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tedious/-/tedious-18.6.1.tgz", "integrity": "sha1-HEo/BsiRvmegMhF+LiUZMobURJY=", "license": "MIT", "dependencies": {"@azure/core-auth": "^1.7.2", "@azure/identity": "^4.2.1", "@azure/keyvault-keys": "^4.4.0", "@js-joda/core": "^5.6.1", "@types/node": ">=18", "bl": "^6.0.11", "iconv-lite": "^0.6.3", "js-md4": "^0.3.2", "native-duplexpair": "^1.0.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">=18"}}, "node_modules/test-exclude": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/test-exclude/-/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/test-exclude/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/test-exclude/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true, "license": "MIT"}, "node_modules/tmpl": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tmpl/-/tmpl-1.0.5.tgz", "integrity": "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/to-buffer": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/to-buffer/-/to-buffer-1.2.1.tgz", "integrity": "sha1-LOZQzbJi6REqGOZdwp3LUTyBVeA=", "license": "MIT", "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/token-types": {"version": "6.0.4", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/token-types/-/token-types-6.0.4.tgz", "integrity": "sha1-FYLvbR13OYzXOMh+o43Vcp+knsU=", "license": "MIT", "dependencies": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}, "engines": {"node": ">=14.16"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}}, "node_modules/ts-api-utils": {"version": "1.4.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-api-utils/-/ts-api-utils-1.4.3.tgz", "integrity": "sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/ts-node/-/ts-node-10.9.2.tgz", "integrity": "sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=", "devOptional": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/tsconfig-paths": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz", "integrity": "sha1-73jhkDkTNEbSRL6sD9ahYy4tEHw=", "dev": true, "license": "MIT", "dependencies": {"json5": "^2.2.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/tsconfig-paths/node_modules/strip-bom": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/tslib/-/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz", "integrity": "sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typeorm": {"version": "0.3.25", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typeorm/-/typeorm-0.3.25.tgz", "integrity": "sha1-mkFvk82g9hKyD4RQ4D1rDhG0Z/s=", "license": "MIT", "dependencies": {"@sqltools/formatter": "^1.2.5", "ansis": "^3.17.0", "app-root-path": "^3.1.0", "buffer": "^6.0.3", "dayjs": "^1.11.13", "debug": "^4.4.0", "dedent": "^1.6.0", "dotenv": "^16.4.7", "glob": "^10.4.5", "sha.js": "^2.4.11", "sql-highlight": "^6.0.0", "tslib": "^2.8.1", "uuid": "^11.1.0", "yargs": "^17.7.2"}, "bin": {"typeorm": "cli.js", "typeorm-ts-node-commonjs": "cli-ts-node-commonjs.js", "typeorm-ts-node-esm": "cli-ts-node-esm.js"}, "engines": {"node": ">=16.13.0"}, "funding": {"url": "https://opencollective.com/typeorm"}, "peerDependencies": {"@google-cloud/spanner": "^5.18.0 || ^6.0.0 || ^7.0.0", "@sap/hana-client": "^2.12.25", "better-sqlite3": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "hdb-pool": "^0.1.6", "ioredis": "^5.0.4", "mongodb": "^5.8.0 || ^6.0.0", "mssql": "^9.1.1 || ^10.0.1 || ^11.0.1", "mysql2": "^2.2.5 || ^3.0.1", "oracledb": "^6.3.0", "pg": "^8.5.1", "pg-native": "^3.0.0", "pg-query-stream": "^4.0.0", "redis": "^3.1.1 || ^4.0.0", "reflect-metadata": "^0.1.14 || ^0.2.0", "sql.js": "^1.4.0", "sqlite3": "^5.0.3", "ts-node": "^10.7.0", "typeorm-aurora-data-api-driver": "^2.0.0 || ^3.0.0"}, "peerDependenciesMeta": {"@google-cloud/spanner": {"optional": true}, "@sap/hana-client": {"optional": true}, "better-sqlite3": {"optional": true}, "hdb-pool": {"optional": true}, "ioredis": {"optional": true}, "mongodb": {"optional": true}, "mssql": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-native": {"optional": true}, "pg-query-stream": {"optional": true}, "redis": {"optional": true}, "sql.js": {"optional": true}, "sqlite3": {"optional": true}, "ts-node": {"optional": true}, "typeorm-aurora-data-api-driver": {"optional": true}}}, "node_modules/typeorm/node_modules/dotenv": {"version": "16.6.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/dotenv/-/dotenv-16.6.1.tgz", "integrity": "sha1-dz8OaVJ6gxXHKF1e5zxEWdIKgCA=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/typeorm/node_modules/glob": {"version": "10.4.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/glob/-/glob-10.4.5.tgz", "integrity": "sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/typeorm/node_modules/uuid": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uuid/-/uuid-11.1.0.tgz", "integrity": "sha1-lUkCi+F1O7k0/JbivKCbtBBa6RI=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}}, "node_modules/typescript": {"version": "4.9.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/typescript/-/typescript-4.9.5.tgz", "integrity": "sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/uid": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uid/-/uid-2.0.2.tgz", "integrity": "sha1-S1eCq/Dy/u78APqIAGsrO3rz47k=", "license": "MIT", "dependencies": {"@lukeed/csprng": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/uint8array-extras": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uint8array-extras/-/uint8array-extras-1.4.0.tgz", "integrity": "sha1-5Cpnim3TNewtIWYTM+1C9ErnzHQ=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=", "devOptional": true, "license": "MIT"}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "integrity": "sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=", "dev": true, "license": "ISC", "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/walker": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/walker/-/walker-1.0.8.tgz", "integrity": "sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=", "dev": true, "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "license": "ISC"}, "node_modules/write-file-atomic": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/wsl-utils": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/wsl-utils/-/wsl-utils-0.1.0.tgz", "integrity": "sha1-h4PU32cdTVA2W+LuTHGRegVXuqs=", "license": "MIT", "dependencies": {"is-wsl": "^3.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yargs/-/yargs-17.7.2.tgz", "integrity": "sha1-mR3zmspnWhkrgW4eA2P5110qomk=", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yn/-/yn-3.1.1.tgz", "integrity": "sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=", "devOptional": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ab-inbev/GHQ_ABI_PEOPLE_TECH_PLATFORM/_packaging/PeoplePlatformNpmMirror/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}