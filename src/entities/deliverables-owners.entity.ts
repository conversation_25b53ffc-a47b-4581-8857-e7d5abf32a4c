import { <PERSON>tity, Column, ManyToMany } from 'typeorm';
import { DeliverableEntity } from './deliverables.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'deliverables_owners' })
export class DeliverableOwnerEntity extends BaseEntity {
  @Column({ nullable: true })
  employeeUuid?: string;

  @Column()
  email: string;

  @Column()
  globalId: number;

  @Column()
  name: string;

  @Column()
  positionTitle: string;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.owners)
  deliverables: DeliverableEntity[];
}
