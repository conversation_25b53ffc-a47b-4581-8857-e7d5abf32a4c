import { Column, CreateDateColumn, DeleteDateColumn, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseEntity {
  @PrimaryColumn({ type: 'uniqueidentifier', default: () => 'NEWID()' })
  uid: string;

  @DeleteDateColumn({ type: 'datetime2', nullable: true, select: false })
  deletedAt?: Date;

  @CreateDateColumn({ type: 'datetime2', select: false })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime2', nullable: true, select: false })
  updatedAt?: Date;

  @Column({ type: 'uniqueidentifier', nullable: true, select: false })
  deletedBy?: string;

  @Column({ type: 'uniqueidentifier', nullable: true, select: false })
  createdBy?: string;

  @Column({ type: 'uniqueidentifier', nullable: true, select: false })
  updatedBy?: string;
}
