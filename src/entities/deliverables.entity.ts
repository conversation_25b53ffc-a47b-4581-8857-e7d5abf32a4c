import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON>ne, Join<PERSON><PERSON>umn, ManyToMany, JoinTable } from 'typeorm';
import { BaseEntity } from './base.entity';
import { DeliverableOwnerEntity } from './deliverables-owners.entity';
import { DeliverableTypeEntity } from './deliverables-types.entity';

@Entity({ name: 'deliverables' })
export class DeliverableEntity extends BaseEntity {
  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  buLevelAggregation?: string;

  @Column({ type: 'nvarchar', nullable: true })
  businessFunction?: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  calculationMethod?: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  content?: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  dataSource?: string;

  @Column({ type: 'datetime2', nullable: true })
  dateEnd?: Date;

  @Column({ type: 'datetime2', nullable: true })
  dateStart?: Date;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  definition?: string;

  @Column({ type: 'nvarchar', nullable: true })
  frequency?: string;

  @Column({ type: 'bit', default: 1 })
  isActive: boolean;

  @Column()
  name: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  paValue?: string;

  @ManyToOne(() => DeliverableTypeEntity, (type) => type.deliverables)
  @JoinColumn({ name: 'deliverableType' })
  deliverableType: DeliverableTypeEntity;

  @ManyToMany(() => DeliverableEntity, (deliverable) => deliverable.deliverables)
  @JoinTable({ name: 'deliverables_deliverables' })
  deliverables: DeliverableEntity[];

  @ManyToMany(() => DeliverableOwnerEntity, (owner) => owner.deliverables, { eager: true })
  @JoinTable({ name: 'deliverables_deliverables_owners' })
  owners: DeliverableOwnerEntity[];
}
