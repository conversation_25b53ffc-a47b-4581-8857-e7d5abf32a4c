import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableEntity } from '../entities/deliverables.entity';
import { BaseRepository } from './base.repository';
import { SortType } from '../enums';

@Injectable()
export class DeliverableRepository extends BaseRepository<DeliverableEntity> {
  constructor(
    @InjectRepository(DeliverableEntity)
    public repository: Repository<DeliverableEntity>
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<any> {
    const deliverable = await this.repository.findOne({
      where: { uid },
      relations: ['deliverableType', 'deliverables']
    });

    if (!deliverable) {
      return null;
    }

    const { deliverableType, ...deliverableData } = deliverable;
    return {
      ...deliverableData,
      type: deliverableType?.code
    };
  }

  async findCompactStandaloneWithFilters(
    search?: string,
    functions?: string[],
    isActive?: boolean,
    types?: string[],
    sortType?: SortType,
    pageNumber: number = 1,
    pageSize: number = 10
  ): Promise<object> {
    const query = this.repository
      .createQueryBuilder('deliverable')
      .leftJoin('deliverable.owners', 'deliverables_owners')
      .leftJoin('deliverable.deliverables', 'deliverables_deliverables')
      .leftJoin('deliverable.deliverableType', 'deliverables_types')
      .andWhere(`deliverable.deliverableType.code NOT IN ('SCOPED_PROJECT_YES_NO')`);

    if (isActive !== undefined) {
      query.andWhere('deliverable.isActive = :isActive', {
        isActive: isActive ? 1 : 0
      });
    } else {
      query.andWhere('deliverable.isActive = 1');
    }

    if (search) {
      query.andWhere('(deliverable.name LIKE :search OR deliverable.calculationMethod LIKE :search)', {
        search: `%${search}%`
      });
    }

    if (functions && functions.length > 0) {
      query.andWhere('deliverable.businessFunction IN (:...functions)', {
        functions
      });
    }

    if (types && types.length > 0) {
      query.andWhere('deliverable.deliverableType.code IN (:...types)', {
        types
      });
    }

    if ([SortType.ASC, SortType.DESC].includes(sortType)) {
      query.orderBy('deliverable.name', sortType);
    } else {
      query.orderBy('deliverable.name', 'ASC');
    }

    const offset = (pageNumber - 1) * pageSize;

    const [entities, totalRecords] = await Promise.all([
      query
        .select([
          'deliverable.uid',
          'deliverable.name',
          'deliverable.businessFunction',
          'deliverables_types.code',
          'deliverable.isActive',
          'deliverable.calculationMethod',
          'deliverable.paValue',
          'deliverable.definition',
          'deliverables_owners.globalId',
          'deliverables_owners.name',
          'deliverables_owners.email',
          'deliverables_owners.positionTitle',
          'deliverables_deliverables.name',
          'deliverable.frequency'
        ])
        .addSelect('deliverables_types.code', 'type')
        .addSelect('deliverables_owners.globalId', 'globalId')
        .addSelect('deliverables_owners.name', 'ownerName')
        .addSelect('deliverables_owners.email', 'ownerEmail')
        .addSelect('deliverables_owners.positionTitle', 'ownerPositionTitle')
        .addSelect('deliverables_deliverables.name', 'deliverableName')
        .skip(offset)
        .take(pageSize)
        .getMany(),
      query.getCount()
    ]);

    const data = entities?.map(({ deliverableType, ...props }) => ({ ...props, type: deliverableType?.code }));

    return {
      data,
      pageNumber,
      pageSize,
      totalRecords
    };
  }

  async findByUids(uids: string[]): Promise<DeliverableEntity[]> {
    if (!uids || uids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('deliverable')
      .select(['deliverable.uid', 'deliverable.name'])
      .where('deliverable.uid IN (:...uids)', { uids })
      .getMany();
  }

  async getFunctions(): Promise<{ function: string }[]> {
    const result = await this.repository
      .createQueryBuilder('deliverable')
      .select('deliverable.businessFunction', 'function')
      .where('deliverable.businessFunction IS NOT NULL')
      .andWhere('deliverable.businessFunction != :emptyString', { emptyString: '' })
      .distinct(true)
      .getRawMany();

    console.log('Functions retrieved:', result);
    return result;
  }
}
